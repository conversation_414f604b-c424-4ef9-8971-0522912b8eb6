Android 10 行为变更
提取时间: 2025-09-02T11:09:42.240744
来源: https://developer.android.com/about/versions/10/behavior-changes-all?hl=zh-cn
================================================================================

此页面由

Cloud Translation API

翻译。

Android Developers

基本知识

行为变更：所有应用

使用集合让一切井井有条

根据您的偏好保存内容并对其进行分类。

Android 10 包含一些可能会影响您的应用的行为变更。本页列出的变更适用于在 Android 10 上运行的应用，无论应用的

targetSdkVersion

如何。您应该测试您的应用，然后根据需要进行更改以适当地支持这些变更。

如果您的应用的 targetSdkVersion 为

或更高，则您还需要支持其他变更。请务必查看

针对以 API 级别 29 为目标的应用的行为变更

，以了解详情。

注意：

除了本页面所列的变更以外，Android 10 还引入了大量基于隐私权的变更和限制，其中包括：

在后台访问设备位置信息

从后台启动 Activity

联系人亲密程度信息

随机分配 MAC 地址

相机元数据

权限模型

这些变更会影响所有应用，并有助于增强用户隐私。如需详细了解如何支持这些变更，请参阅

隐私权变更

页面。

限制非 SDK 接口

为了帮助确保应用的稳定性和兼容性，Android 平台开始限制应用在 Android 9（API 级别 28）中使用

非 SDK 接口

。Android 10 包含更新后的受限非 SDK 接口列表（基于与 Android 开发者之间的协作以及最新的内部测试）。我们的目标是在限制使用非 SDK 接口之前确保有可用的公开替代方案。

如果您不打算以 Android 10（API 级别 29）为目标平台，那么其中一些变更可能不会立即对您产生影响。然而，虽然您目前仍可以使用一些非 SDK 接口（

具体取决于应用的目标 API 级别

），但只要您使用任何非 SDK 方法或字段，终归存在导致应用出问题的显著风险。

如果您不确定自己的应用是否使用了非 SDK 接口，则可以

测试该应用

，进行确认。如果您的应用依赖于非 SDK 接口，则应该开始计划迁移到 SDK 替代方案。然而，我们知道某些应用具有使用非 SDK 接口的有效用例。如果您无法为应用中的某项功能找到使用非 SDK 接口的替代方案，则应该

请求新的公共 API

如需了解详情，请参阅

Android 10 中有关限制非 SDK 接口的更新

针对非 SDK 接口的限制

手势导航

从 Android 10 开始，用户可以在设备中启用手势导航。用户启用后，手势导航会影响设备上的所有应用，无论应用是否以 API 级别 29 为目标平台。例如，如果用户从屏幕边缘向内滑动，系统会将该手势解读为“返回”导航，除非应用针对屏幕的相应部分

明确替换该手势

为了确保您的应用与手势导航兼容，您需要将应用内容扩展到屏幕边缘，并适当地处理存在冲突的手势。
如需了解详情，请参阅

文档。

NDK

Android 10 包含 NDK 方面的以下变更。

共享对象不得包含文本重定位

Android 6.0（API 级别 23）

已禁止在共享对象中使用

文本重定位。代码必须按原样加载，且不得修改。此变更可以缩短应用的加载时间并提高安全性。

SELinux 针对以 Android 10 或更高版本为目标平台的应用强制执行此限制。如果这些应用继续使用包含文本重定位的共享对象，则它们出现中断的风险较高。

Bionic 库和动态链接器路径变更

从 Android 10 开始，多个路径不再采用常规文件形式，而是采用符号链接形式。如果应用一直以来依赖的都是采用常规文件形式的路径，则可能会出现中断：

/system/lib/libc.so

/apex/com.android.runtime/lib/bionic/libc.so

/system/lib/libm.so

/apex/com.android.runtime/lib/bionic/libm.so

/system/lib/libdl.so

/apex/com.android.runtime/lib/bionic/libdl.so

/system/bin/linker

/apex/com.android.runtime/bin/linker

这些变更也会影响文件的 64 位版本，对于这些版本，系统会将

lib/

替换为

lib64/

为了确保兼容性，符号链接会基于旧路径提供。例如，

是指向

的符号链接。因此，

dlopen(“/system/lib/libc.so”)

会继续正常运行，但当应用尝试通过读取

/proc/self/maps

或类似方式来检查已加载的库时，会发现差异。虽然这种情况并不常见，但我们发现有些应用会这样做，作为其反黑客流程的一部分。如果是这样，则应该将

/apex/…

路径添加为 Bionic 文件的有效路径。

系统二进制文件/库会映射到只执行内存

从 Android 10 开始，系统二进制文件和库的可执行部分会映射到只执行（不可读取）内存，作为防范代码重用攻击的一种安全强化技术。如果您的应用针对已标记为只执行的内存段执行读取操作（无论此读取操作是来自 bug、漏洞还是有意的内存检查），系统都会向您的应用发送

SIGSEGV

信号。

您可以通过检查

/data/tombstones/

中的相关 tombstone 文件来确定此行为是否会导致崩溃。与只执行相关的崩溃包含以下中止消息：

Cause: execute-only (no-read) memory access error; likely due to data in .text.

要解决此问题以执行内存检查等操作，可以通过调用

mprotect()

将只执行内存段标记为“读取+执行”。不过，我们强烈建议您事后将其重新设为只执行，因为此访问权限设置可以更好地保护您的应用和用户。

Android 10 包含安全方面的以下变更。

TLS 1.3 默认处于启用状态

在 Android 10 及更高版本中，系统默认会为所有 TLS 连接启用 TLS 1.3。以下是有关 TLS 1.3 实现的一些重要的详细信息：

TLS 1.3 加密套件不可自定义。在启用 TLS 1.3 后，受支持的 TLS 1.3 加密套件会始终保持启用状态。任何尝试通过调用

setEnabledCipherSuites()

停用该加密套件的操作均会被忽略。

在协商 TLS 1.3 时，系统会在将会话添加到会话缓存之前调用

HandshakeCompletedListener

对象。

（在 TLS 1.2 和之前的其他版本中，系统会在将会话添加到会话缓存之后调用这些对象。）

在某些情况下，

SSLEngine

实例会在之前的 Android 版本中抛出

SSLHandshakeException

，而这些实例在 Android 10 及更高版本中会改为抛出

SSLProtocolException

不支持 0-RTT 模式。

如有需要，您可以通过调用

SSLContext.getInstance("TLSv1.2")

来获取已停用 TLS 1.3 的

SSLContext

。
您还可以对相关对象调用

setEnabledProtocols()

，从而为每个连接启用或停用协议版本。

TLS 不信任使用 SHA-1 签名的证书

在 Android 10 中，使用 SHA-1 哈希算法的证书在 TLS 连接中不受信任。自 2016 年以来，根 CA 未再颁发过此类证书，因为它们不再受 Chrome 或其他主流浏览器的信任。

如果某网站使用的是 SHA-1 证书，则任何尝试连接该网站的操作都将失败。

KeyChain 行为变更和改进

当 TLS 服务器在 TLS 握手中发送证书请求消息时，某些浏览器（如 Google Chrome）允许用户选择证书。从 Android 10 开始，

KeyChain

对象会在调用

KeyChain.choosePrivateKeyAlias()

时信任颁发机构和密钥规范参数，以向用户显示证书选择提示。需要注意的是，此提示不包含不符合服务器规范的选项。

如果没有可供用户选择的证书（例如，没有与服务器规范匹配的证书，或者设备上未安装任何证书），则完全不会出现证书选择提示。

此外，在 Android 10 或更高版本上，无需具备设备屏幕锁定功能，就能将密钥或 CA 证书导入

对象中。

其他 TLS 和加密更改

Android 10 中引入的 TLS 和加密库方面的一些细小变更包括：

AES/GCM/NoPadding 和 ChaCha20/Poly1305/NoPadding 加密会从

getOutputSize()

中返回更准确的缓冲区大小。

使用 TLS 1.2 或更高版本的最高协议在尝试连接时会忽略

TLS_FALLBACK_SCSV

加密套件。由于 TLS 服务器实现方面的改进，我们不建议尝试 TLS 外部回退。不过，我们建议依赖于 TLS 版本协商。

ChaCha20-Poly1305 是 ChaCha20/Poly1305/NoPadding 的别名。

带有尾随点的主机名不属于有效的 SNI 主机名。

为证书响应选择签名密钥时，将遵循

CertificateRequest

中的 supported_signature_algorithms 扩展。

不透明的签名密钥（如 Android 密钥库中的密钥）可在 TLS 中与 RSA-PSS 签名一起使用。

WLAN 直连广播

在 Android 10 中，以下与

WLAN 直连

相关的广播不具有粘性：

WIFI_P2P_CONNECTION_CHANGED_ACTION

WIFI_P2P_THIS_DEVICE_CHANGED_ACTION

如果您的应用依赖于在注册时接收这些广播（因为其之前一直是固定的），请在初始化时使用适当的

get()

方法获取信息。

WLAN 感知功能

Android 10 扩大了支持范围，现在可以使用 WLAN 感知数据路径轻松创建 TCP/UDP 套接字。要创建连接到

ServerSocket

的 TCP/UDP 套接字，客户端设备需要知道服务器的 IPv6 地址和端口。这在之前需要通过频外方式进行通信（例如使用 BT 或 WLAN 感知第 2 层消息传递），或者使用其他协议（例如 mDNS）通过频内方式发现。而借助 Android 10，可以将此类消息作为网络设置的一部分进行传递。

服务器可以执行以下任一操作：

初始化

并设置或获取要使用的端口。

将端口信息指定为 WLAN 感知网络请求的一部分。

以下代码示例显示了如何将端口信息指定为网络请求的一部分：

Kotlin

val

WifiAwareNetworkSpecifier

Builder

discoverySession

peerHandle

setPskPassphrase

"some-password"

setPort

localPort

build

myNetworkRequest

NetworkRequest

addTransportType

NetworkCapabilities

TRANSPORT_WIFI_AWARE

setNetworkSpecifier

Java

new

();

some

password

getLocalPort

())

然后，客户端会执行 WLAN 感知网络请求来获取服务器提供的 IPv6 和端口：

callback

object

ConnectivityManager

NetworkCallback

override

fun

onAvailable

network

Network

...

onLinkPropertiesChanged

linkProperties

LinkProperties

onCapabilitiesChanged

networkCapabilities

transportInfo

WifiAwareNetworkInfo

peerAddress

peerIpv6Addr

peerPort

port

onLost

connMgr

requestNetwork

networkRequest

@Override

public

void

TransportInfo

getTransportInfo

instanceof

info

Inet6Address

getPeerIpv6Addr

int

getPort

Go 设备上的 SYSTEM_ALERT_WINDOW

在 Android 10（Go 版本）设备上运行的应用无法获得

SYSTEM_ALERT_WINDOW

权限。这是因为绘制叠加层窗口会使用过多的内存，这对低内存 Android 设备的性能十分有害。

如果在搭载 Android 9 或更低版本的 Go 版设备上运行的应用获得了

权限，则即使设备升级到 Android 10，也会保留此权限。不过，尚不具有此权限的应用在设备升级后便无法获得此权限了。

如果 Go 设备上的应用发送了包含操作

ACTION_MANAGE_OVERLAY_PERMISSION

的 intent，系统会自动拒绝该请求，并引导用户前往

界面，该界面会显示“不允许使用此权限，因为这会降低设备速度”。如果 Go 设备上的应用调用

Settings.canDrawOverlays()

，则此方法始终返回 false。同样，这些限制不适用于在设备升级到 Android 10 之前便已收到

权限的应用。

关于以旧版 Android 系统为目标平台的应用的警告

在搭载 Android 10 或更高版本的设备上，如果用户首次运行以 Android 5.1（API 级别 22）或更低版本为目标平台的应用，则会看到警告。如果此应用要求用户授予权限，则系统会先向用户提供调整应用权限的机会，然后才会允许此应用首次运行。

由于 Google Play 的

目标 API 方面的要求

，用户只有在运行最近未更新的应用时才会看到这些警告。对于通过其他商店分发的应用，我们也将于 2019 年引入类似的目标 API 方面的要求。如需详细了解这些要求，请参阅

在 2019 年扩展目标 API 级别方面的要求

移除了 SHA-2 CBC 加密套件

以下 SHA-2 CBC 加密套件已从平台中移除：

TLS_RSA_WITH_AES_128_CBC_SHA256

TLS_RSA_WITH_AES_256_CBC_SHA256

TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256

TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384

TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256

TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384

这些加密套件不如使用 GCM 的类似加密套件安全，并且大多数服务器要么同时支持这些加密套件的 GCM 变体和 CBC 变体，要么二者均不支持。

应用使用情况

Android 10 引入了与应用使用情况相关的以下行为变更：

UsageStats 应用使用情况的改进

-






























































































































<0x0A

此外，Android 10 可以正确地跟踪免安装应用的使用情况。

按应用开启灰度模式

- Android 10 可针对各个应用设置灰度显示模式。

按应用开启干扰模式

-















































































































<0

暂停和播放

- 在 Android 10 中，暂停的应用无法播放音频。

HTTPS 连接变更

如果运行 Android 10 的应用将

null

传递给

setSSLSocketFactory()

，则会出现

IllegalArgumentException

。在以前的版本中，将

与传入当前的

默认工厂

效果相同。

android.preference 库已弃用

从 Android 10 开始，将弃用

android.preference

库。开发者应该改为使用 AndroidX preference 库，这是

Android Jetpack

的一部分。如需获取其他有助于迁移和开发的资源，请查看经过更新的

设置指南

以及我们的

公开示例应用

参考文档

ZIP 文件实用程序库变更

Android 10 对

java.util.zip

软件包（用于处理 ZIP 文件）中的类进行了以下变更。这些变更会让库的行为在 Android 和使用

的其他平台之间更加一致。

Inflater

在之前的版本中，如果

类中的某些方法在调用

end()

之后被调用，则会抛出

IllegalStateException

。在 Android 10 中，这些方法会改为抛出

NullPointerException

ZipFile

在 Android 10 及更高版本中，如果提供的 ZIP 文件不包含任何文件，则采用

File

Charset

类型实参的

构造函数

不会抛出

ZipException

ZipOutputStream

在 Android 10 及更高版本中，如果

finish()

方法尝试为不包含任何文件的 ZIP 文件写入输出流，则不会抛出

摄像头变更

很多使用摄像头的应用都会假定如果设备采用纵向配置，则物理设备也会处于纵向，正如

摄像头方向

中所述。在过去可以做出这样的假定，但随着可用的设备类型（例如可折叠设备）的扩展，这一情况发生了变化。针对这些设备做出这样的假定可能导致相机取景器的显示产生错误的旋转和/或缩放。

以 API 级别 24 或更高级别为目标平台的应用应该明确设置

android:resizeableActivity

，并提供必要的功能来处理多窗口操作。

电池用量跟踪

从 Android 10 开始，

SystemHealthManager

会在设备在

重大充电事件

后拔下电源插头时重置其电池用量统计信息。一般来说，重大充电事件指的是设备电池已充满，或者设备电量从几乎耗尽变为即将充满。

在 Android 10 之前，无论何时拔下设备电源插头，无论电池电量有多微小的变化，电池用量统计信息都会重置。

Android Beam 已弃用

在 Android 10 中，我们正式弃用了 Android Beam，这是一项旧版功能，可通过近距离无线通信 (NFC) 在多个设备之间启动数据共享。我们还弃用了一些相关的 NFC API。Android Beam 仍可供需要的设备制造商合作伙伴使用，但它已不再处于积极的开发阶段。不过，Android 仍将继续支持其他的 NFC 功能和 API，并且从标签和付款中读取数据等使用场景仍将继续按预期执行。

java.math.BigDecimal.stripTrailingZeros() 行为变更

如果输入值为零，

BigDecimal.stripTrailingZeros()

不再将尾随零作为特殊情况保留。

java.util.regex.Matcher 和 Pattern 行为变更

当输入开头存在零宽度匹配时，

split()

的结果已更改为不再以空

String

（“”）开头。这也会影响

String.split()

。例如，

"x".split("")

现在返回

{"x"}

，而在旧版 Android 上，它过去返回

{"", "x"}

"aardvark".split("(?=a)"

{"a", "ardv", "ark"}

，而不是

{"", "a", "ardv", "ark"}

还改进了无效实参的异常行为：

如果替换

以单独的反斜杠结尾（这是不允许的），

appendReplacement(StringBuffer, String)

现在会抛出

而不是

IndexOutOfBoundsException

。如果替换的

结尾，则现在会抛出相同的异常。之前，在这种情况下不会抛出任何异常。

Matcher

replaceFirst(null)

不再在

上调用

reset()

。现在，如果没有匹配项，也会抛出

。之前，仅在存在匹配项时才会抛出此异常。

start(int group)

end(int group)

group(int group)

现在会在组索引超出范围时抛出更一般的

。
之前，这些方法会抛出

ArrayIndexOutOfBoundsException

GradientDrawable 的默认角度现在为 TOP_BOTTOM

在 Android 10 中，如果您在 XML 中定义了

GradientDrawable

且未提供角度测量值，则渐变方向默认设置为

TOP_BOTTOM

。这与之前的 Android 版本不同，在之前的版本中，默认值为

LEFT_RIGHT

作为一种解决方法，如果您更新到最新版本的

AAPT2

，则该工具会在未指定角度测量值的情况下为旧版应用设置 0 的角度测量值。

使用默认 SUID 记录序列化对象的日志

从 Android 7.0（API 级别 24）开始，平台

修复了可序列化对象的默认

serialVersionUID

。此修复不会影响以 API 级别 23 或更低级别为目标平台的应用。

从 Android 10 开始，如果应用以 API 级别 23 或更低级别为目标平台，并依赖于旧的错误默认

，系统会记录警告并建议代码修复。

具体而言，如果满足以下所有条件，系统会记录警告：

应用以 API 级别 23 或更低级别为目标平台。

类被序列化。

序列化类使用默认

，而不是显式设置

默认值

与应用以 API 级别 24 或更高级别为目标平台时的

不同。

系统会针对每个受影响的类记录一次此警告。
警告消息包含建议的修复措施，即明确将

设置为如果应用以 API 级别 24 或更高级别为目标平台时会计算出的默认值。使用该修复程序可确保，如果以 API 级别 23 或更低级别为目标平台的应用序列化了相应类的对象，以 24 或更高级别为目标平台的应用将能够正确读取该对象，反之亦然。

java.io.FileChannel.map() 更改

FileChannel.map()

不再支持非标准文件（例如

/dev/zero

），这些文件的大小无法使用

truncate()

进行更改。之前的 Android 版本会生吞掉

返回的 errno，但 Android 10 会抛出 IOException。如果您需要旧行为，则必须使用原生代码。

本页面上的内容和代码示例受

内容许可

部分所述许可的限制。Java 和 OpenJDK 是 Oracle 和/或其关联公司的注册商标。

最后更新时间 (UTC)：2025-08-27。

[[["易于理解","easyToUnderstand","thumb-up"],["解决了我的问题","solvedMyProblem","thumb-up"],["其他","otherUp","thumb-up"]],[["没有我需要的信息","missingTheInformationINeed","thumb-down"],["太复杂/步骤太多","tooComplicatedTooManySteps","thumb-down"],["内容需要更新","outOfDate","thumb-down"],["翻译问题","translationIssue","thumb-down"],["示例/代码问题","samplesCodeIssue","thumb-down"],["其他","otherDown","thumb-down"]],["最后更新时间 (UTC)：2025-08-27。"],[],[],null,[]]

[代码块] 29

[代码块] valss=ServerSocket()valns=WifiAwareNetworkSpecifier.Builder(discoverySession,peerHandle).setPskPassphrase("some-password").setPort(ss.localPort).build()valmyNetworkRequest=NetworkRequest.Builder().addTransportType(NetworkCapabilities.TRANSPORT_WIFI_AWARE).setNetworkSpecifier(ns).build()

[代码块] ServerSocketss=newServerSocket();WifiAwareNetworkSpecifierns=newWifiAwareNetworkSpecifier.Builder(discoverySession,peerHandle).setPskPassphrase(“some-password”).setPort(ss.getLocalPort()).build();NetworkRequestmyNetworkRequest=newNetworkRequest.Builder().addTransportType(NetworkCapabilities.TRANSPORT_WIFI_AWARE).setNetworkSpecifier(ns).build();

[代码块] valcallback=object:ConnectivityManager.NetworkCallback(){overridefunonAvailable(network:Network){...}overridefunonLinkPropertiesChanged(network:Network,linkProperties:LinkProperties){...}overridefunonCapabilitiesChanged(network:Network,networkCapabilities:NetworkCapabilities){...valti=networkCapabilities.transportInfoif(tiisWifiAwareNetworkInfo){valpeerAddress=ti.peerIpv6AddrvalpeerPort=ti.port}}overridefunonLost(network:Network){...}};connMgr.requestNetwork(networkRequest,callback)

[代码块] callback=newConnectivityManager.NetworkCallback(){@OverridepublicvoidonAvailable(Networknetwork){...}@OverridepublicvoidonLinkPropertiesChanged(Networknetwork,LinkPropertieslinkProperties){...}@OverridepublicvoidonCapabilitiesChanged(Networknetwork,NetworkCapabilitiesnetworkCapabilities){...TransportInfoti=networkCapabilities.getTransportInfo();if(tiinstanceofWifiAwareNetworkInfo){WifiAwareNetworkInfoinfo=(WifiAwareNetworkInfo)ti;Inet6AddresspeerAddress=info.getPeerIpv6Addr();intpeerPort=info.getPort();}}@OverridepublicvoidonLost(Networknetwork){...}};connMgr.requestNetwork(networkRequest,callback);

[代码块] $

[代码] valss=ServerSocket()valns=WifiAwareNetworkSpecifier.Builder(discoverySession,peerHandle).setPskPassphrase("some-password").setPort(ss.localPort).build()valmyNetworkRequest=NetworkRequest.Builder().addTransportType(NetworkCapabilities.TRANSPORT_WIFI_AWARE).setNetworkSpecifier(ns).build()

[代码] ServerSocketss=newServerSocket();WifiAwareNetworkSpecifierns=newWifiAwareNetworkSpecifier.Builder(discoverySession,peerHandle).setPskPassphrase(“some-password”).setPort(ss.getLocalPort()).build();NetworkRequestmyNetworkRequest=newNetworkRequest.Builder().addTransportType(NetworkCapabilities.TRANSPORT_WIFI_AWARE).setNetworkSpecifier(ns).build();

[代码] valcallback=object:ConnectivityManager.NetworkCallback(){overridefunonAvailable(network:Network){...}overridefunonLinkPropertiesChanged(network:Network,linkProperties:LinkProperties){...}overridefunonCapabilitiesChanged(network:Network,networkCapabilities:NetworkCapabilities){...valti=networkCapabilities.transportInfoif(tiisWifiAwareNetworkInfo){valpeerAddress=ti.peerIpv6AddrvalpeerPort=ti.port}}overridefunonLost(network:Network){...}};connMgr.requestNetwork(networkRequest,callback)

[代码] callback=newConnectivityManager.NetworkCallback(){@OverridepublicvoidonAvailable(Networknetwork){...}@OverridepublicvoidonLinkPropertiesChanged(Networknetwork,LinkPropertieslinkProperties){...}@OverridepublicvoidonCapabilitiesChanged(Networknetwork,NetworkCapabilitiesnetworkCapabilities){...TransportInfoti=networkCapabilities.getTransportInfo();if(tiinstanceofWifiAwareNetworkInfo){WifiAwareNetworkInfoinfo=(WifiAwareNetworkInfo)ti;Inet6AddresspeerAddress=info.getPeerIpv6Addr();intpeerPort=info.getPort();}}@OverridepublicvoidonLost(Networknetwork){...}};connMgr.requestNetwork(networkRequest,callback);

[代码] 29

[代码] $