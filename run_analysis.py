#!/usr/bin/env python3
"""
启动脚本 - 同时运行RSS监控和AI分析
"""

import subprocess
import sys
import time
import threading
import logging
from pathlib import Path

def setup_logging():
    """设置日志"""
    import os
    os.makedirs('logs', exist_ok=True)
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/run_analysis.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def run_rss_monitor():
    """运行RSS监控"""
    try:
        logging.info("启动RSS监控...")
        subprocess.run([sys.executable, "apple_release_monitor.py"], check=True)
    except Exception as e:
        logging.error(f"RSS监控出错: {e}")

def run_ai_analyzer():
    """运行AI分析器"""
    try:
        logging.info("启动iOS AI分析器...")
        subprocess.run([sys.executable, "analyzer_manager.py", "config/config_ios.yaml"], check=True)
    except Exception as e:
        logging.error(f"AI分析器出错: {e}")

def main():
    """主函数"""
    setup_logging()
    
    # 检查必要文件
    required_files = ["apple_release_monitor.py", "analyzer_manager.py", "config/config_ios.yaml"]
    for file in required_files:
        if not Path(file).exists():
            logging.error(f"缺少必要文件: {file}")
            return
    
    logging.info("开始启动Apple Developer Release监控和AI分析系统...")
    
    # 创建线程
    rss_thread = threading.Thread(target=run_rss_monitor, daemon=True)
    ai_thread = threading.Thread(target=run_ai_analyzer, daemon=True)
    
    try:
        # 启动线程
        rss_thread.start()
        time.sleep(2)  # 稍等一下再启动AI分析器
        ai_thread.start()
        
        logging.info("所有服务已启动，按Ctrl+C停止...")
        
        # 等待线程
        while True:
            time.sleep(1)
            if not rss_thread.is_alive() and not ai_thread.is_alive():
                break
    
    except KeyboardInterrupt:
        logging.info("正在停止服务...")
    except Exception as e:
        logging.error(f"运行出错: {e}")

if __name__ == "__main__":
    main()
