Android 15 行为变更
提取时间: 2025-09-02T11:09:28.851504
来源: https://developer.android.com/about/versions/15/behavior-changes-all?hl=zh-cn
================================================================================

此页面由

Cloud Translation API

翻译。

Android Developers

基本知识

行为变更：所有应用

使用集合让一切井井有条

根据您的偏好保存内容并对其进行分类。

Android 15 平台包含一些可能会影响您的应用的行为变更。以下行为变更将影响在 Android 15 上运行的

所有应用，无论采用哪种

targetSdkVersion

都不例外。您应该测试您的应用，然后根据需要进行修改，以适当地支持这些变更。

此外，请务必查看

仅影响以 Android 15 为目标平台的应用的行为变更

列表。

核心功能

Android 15 修改或扩展了 Android 系统的各种核心功能。

软件包停止状态的更改

软件包

FLAG_STOPPED

状态（用户可以通过在 AOSP build 中长按应用图标并选择“强制停止”来启用此状态）的用途一直是让应用保持在此状态，直到用户通过直接启动应用或间接与应用互动（通过 Sharesheet 或 widget、将应用选择为动态壁纸等）来明确将应用从此状态移除。在 Android 15 中，我们更新了系统行为，使其与此预期行为保持一致。应用应仅通过直接或间接的用户操作从停止状态移除。

为了支持预期行为，除了现有限制之外，当应用在搭载 Android 15 的设备上进入停止状态时，系统还会取消所有

待处理 intent

。当用户的操作将应用从停止状态移除时，系统会将

ACTION_BOOT_COMPLETED

广播传送到应用，以便应用有机会重新注册所有待处理 intent。

您可以调用新的

ApplicationStartInfo.wasForceStopped()

方法来确认应用是否已进入停止状态。

支持 16 KB 页面大小

从历史上看，Android 仅支持 4 KB 内存页面大小，这优化了系统内存性能，以适应 Android 设备通常拥有的平均总内存量。从 Android 15 开始，AOSP 支持配置为使用 16 KB 页面大小的设备（16 KB 设备）。如果您的应用直接或通过 SDK 间接使用任何

NDK

库，则需要重新构建应用，才能在这些 16 KB 设备上运行。

随着设备制造商不断制造出具有更大物理内存 (RAM) 的设备，许多此类设备将采用 16 KB（最终甚至更大）的页面大小来优化设备性能。添加对 16 KB 页面大小设备的支持，可让您的应用在这些设备上运行，并帮助您的应用受益于相关的性能改进。如果不重新编译，应用将无法在未来 Android 版本的 16 KB 设备上运行。

为帮助您为应用添加支持，我们提供了相关指南，介绍了如何

检查应用是否受到影响

、如何

重新构建应用

（如果适用），以及如何使用模拟器（包括 Android 模拟器的 Android 15 系统映像）

在 16 KB 环境中测试应用

优势和性能提升

配置为使用 16 KB 页面大小的设备平均会使用略多一些的内存，但系统和应用的性能也会得到各种提升：

缩短了系统内存压力时的应用启动时间：平均降低了 3.16%；对于我们测试的某些应用而言，改进幅度更大（最高可达 30%）

应用启动期间的功耗降低：平均降低了 4.56%

相机启动更快：热启动速度平均提高了 4.48%，冷启动速度平均提高了 6.60%

缩短了系统启动时间：平均缩短了 8%（约 950 毫秒）

这些改进基于我们的初始测试，实际设备上的结果可能会有所不同。随着测试的继续进行，我们将进一步分析应用的潜在收益。

检查您的应用是否受到影响

如果您的应用

使用了任何原生代码

，则应

重新构建应用，使其支持 16 KB 设备

。如果您不确定自己的应用是否使用了原生代码，可以

使用 APK 分析器来确定是否存在任何原生代码

，然后

检查您找到的任何共享库的 ELF 段对齐情况

。Android Studio 还提供了一些功能，可帮助您

自动检测对齐问题

如果您的应用仅使用以 Java 或 Kotlin 编程语言编写的代码（包括所有库或 SDK），则该应用已支持 16 KB 设备。不过，我们建议您

，以验证应用行为是否出现意外的回归。

某些应用支持私密空间所需的更改

私密空间

是 Android 15 中推出的一项新功能，可让用户在设备上创建一个单独的空间，在额外的身份验证层保护下，防止敏感应用遭到窥探。由于私密空间中的应用具有受限的公开范围，因此某些类型的应用需要执行额外的步骤，才能查看和与用户私密空间中的应用互动。

所有应用

由于私密空间中的应用会保存在单独的用户资料中（类似于

工作资料

），因此应用不应假定其任何未位于主资料中的已安装副本都位于工作资料中。如果您的应用包含与工作资料应用相关的逻辑，并且做出了上述假设，则需要调整此逻辑。

医疗应用

当用户锁定私密空间时，私密空间中的所有应用都会停止运行，并且这些应用无法执行前台或后台活动，包括显示通知。此行为可能会严重影响安装在私密空间中的医疗应用的使用和功能。

私密空间设置体验会向用户发出警告，告知私密空间不适合需要执行关键前台或后台活动的应用，例如显示医疗应用发送的通知。不过，应用无法确定自己是否在私密空间中使用，因此无法在这种情况下向用户显示警告。

因此，如果您开发的是医疗应用，请检查此功能可能会对您的应用产生哪些影响，并采取适当的措施（例如告知用户不要在私密空间中安装您的应用），以免中断关键的应用功能。

启动器应用

如果您开发的是启动器应用，则必须执行以下操作，才能看到私密空间中的应用：

您的应用必须被指定为设备的默认启动器应用，即具有

ROLE_HOME

角色。

您的应用必须

在应用的清单文件中

ACCESS_HIDDEN_PROFILES

普通权限。

权限的启动器应用必须处理以下私密空间用例：

您的应用必须为安装在私密空间中的应用提供单独的启动器容器。使用

getLauncherUserInfo()

方法确定要处理的用户个人资料类型。

用户必须能够隐藏和显示私密空间容器。

用户必须能够锁定和解锁私密空间容器。使用

requestQuietModeEnabled()

方法锁定（通过传递

true

）或解锁（通过传递

false

）私密空间。

在锁定状态下，私密空间容器中的任何应用都应不可见，也无法通过搜索等机制被发现。您的应用应为

ACTION_PROFILE_AVAILABLE

ACTION_PROFILE_UNAVAILABLE

注册接收器

，并在私密空间容器的锁定或解锁状态发生变化时更新应用中的界面。这两种状态的广播都包含

EXTRA_USER

，您的应用可以使用该常量来引用不公开个人资料的用户。

您还可以使用

isQuietModeEnabled()

方法检查私密空间个人资料是否已锁定。

应用商店应用

私密空间包含一个“安装应用”按钮，用于启动隐式 intent 以将应用安装到用户的私密空间。为了让应用能够接收此隐式 intent，请在应用的清单文件中声明一个

<intent-filter>

，并将

<category>

CATEGORY_APP_MARKET

移除了基于 PNG 的表情符号字体

我们移除了基于 PNG 的旧版表情符号字体文件 (

NotoColorEmojiLegacy.ttf

)，只保留了基于矢量的文件。从 Android 13（API 级别 33）开始，系统表情符号渲染程序使用的表情符号字体文件

已从基于 PNG 的文件更改为基于矢量的文件

。出于兼容性原因，系统在 Android 13 和 14 中保留了旧版字体文件，以便具有自己的字体渲染程序的应用在能够升级之前继续使用旧版字体文件。

如需检查您的应用是否受到影响，请在应用的代码中搜索对

文件的引用。

您可以通过多种方式自适应应用：

使用平台 API 进行文本渲染。您可以将文本渲染到基于位图的

Canvas

，并在必要时使用该

获取原始图片。

为您的应用添加 COLRv1 字体支持。FreeType 开源库在

2.13.0 版

及更高版本中支持 COLRv1。

作为最后的手段，您可以将旧版表情符号字体文件 (

NotoColorEmoji.ttf

) 捆绑到 APK 中，但在这种情况下，您的应用将缺少最新的表情符号更新。如需了解详情，请参阅

Noto Emoji GitHub 项目页面

将最低目标 SDK 版本从 23 提升至 24

Android 15 基于

在 Android 14 中进行的更改

，并扩展了
安全性。在 Android 15 中，
无法安装低于 24 的

。
要求应用符合现代 API 级别有助于确保更好的安全性和
保护隐私。

恶意软件通常会以较低的 API 级别为目标平台，以绕过安全和隐私
更高的 Android 版本中引入的保护机制。例如，有些恶意软件应用使用

22，以避免受到 Android 6.0 Marshmallow（API 级别 23）在 2015 年引入的运行时权限模型的约束。这项 Android 15 变更使恶意软件更难以规避安全和隐私权方面的改进限制。尝试安装以较低 API 级别为目标平台的应用将导致安装失败，并且 Logcat 中会显示如下所示的消息：

INSTALL_FAILED_DEPRECATED_SDK_VERSION: App package must target at least SDK version 24, but found 7

在升级到 Android 15 的设备上，

级别较低的任何应用
安装在 Google Play 上

如果您需要测试以旧版 API 级别为目标平台的应用，请使用以下 ADB 命令：

adb install --bypass-low-target-sdk-block

FILENAME

.apk

安全和隐私设置

Android 15 introduces robust measures to combat one-time passcode (OTP) fraud
and to protect the user's sensitive content, focusing on hardening the
Notification Listener Service and screenshare protections. Key enhancements
include redacting OTPs from notifications accessible to untrusted apps, hiding
notifications during screenshare, and securing app activities when OTPs are
posted. These changes aim to keep the user's sensitive content safe from
unauthorized actors.

Developers need to be aware of the following to ensure their apps are compatible
with the changes in Android 15:

OTP Redaction

Android will stop untrusted apps that implement a

NotificationListenerService

from reading unredacted content
from notifications where an OTP has been detected. Trusted apps such as
companion device manager associations are exempt from these restrictions.

Screenshare Protection

Notification content is hidden during screen sharing sessions to preserve
the user's privacy. If the app implements

setPublicVersion()

, Android shows the public version of
the notification which serves as a replacement notification in insecure
contexts. Otherwise, the notification content is redacted without any
further context.

Sensitive content like password input is hidden from remote viewers to
prevent revealing the user's sensitive information.

Activities from apps that post notifications during screenshare where an OTP
has been detected will be hidden. App content is hidden from the remote
viewer when launched.

Beyond Android's automatic identification of sensitive fields, developers
can manually mark parts of their app as sensitive using

setContentSensitivity

, which is hidden from remote
viewers during screenshare.

Developers can choose to toggle the

Disable screen share protections

option under

Developer Options

to be exempted from the screenshare
protections for demo or testing purposes. The default system screen recorder
is exempted from these changes, since the recordings remain on-device.

摄像头和媒体

Android 15 对所有应用的相机和媒体行为做出了以下更改。

当达到资源限制时，直接播放和分流播放音频会使之前打开的直接或分流音频轨道失效

在 Android 15 之前，如果某个应用在另一个应用播放音频且达到资源限制时请求直接或分流音频播放，该应用将无法打开新的

AudioTrack

从 Android 15 开始，当应用请求直接播放或分流播放且达到资源限制时，系统会使任何当前打开的

对象失效，以防止执行新轨道请求。

（直接音轨和分流音轨通常会打开，以播放压缩音频格式。播放直接音频的常见用例包括通过 HDMI 将编码的音频流式传输到电视。分流轨道通常用于在具有硬件 DSP 加速的移动设备上播放压缩音频。）

用户体验和系统界面

Android 15 包含一些旨在打造更一致、更直观的用户体验的变更。

为选择启用的应用启用了预测性返回动画

从 Android 15 开始，

预测性返回动画

的开发者选项已被移除。现在，如果应用已完全或在 activity 级别

选择启用预测性返回手势

，则系统会为其显示“返回主屏幕”“跨任务”和“跨 activity”等系统动画。如果您的应用受到了影响，请执行以下操作：

确保您的应用已正确迁移，以使用预测性返回手势。

确保 fragment 转场效果可与预测性返回导航搭配使用。

请弃用动画和框架过渡，改用动画和 androidx 过渡。

FragmentManager

不认识的返回堆栈迁移。请改用由

或 Navigation 组件管理的返回堆栈。

当用户强制停止应用时，widget 会被停用

如果用户在搭载 Android 15 的设备上强制停止应用，系统会暂时停用该应用的所有微件。这些 widget 会灰显，用户无法与其互动。这是因为，从 Android 15 开始，当系统强制停止应用时，会取消应用的所有待处理 intent。

系统会在用户下次启动应用时重新启用这些微件。

如需了解详情，请参阅

对软件包停止状态的更改

媒体投影状态栏功能块会提醒用户屏幕共享、投屏和录制功能

屏幕投影漏洞会泄露用户的私密数据（例如财务信息），因为用户不知道自己的设备屏幕正在共享。

对于搭载 Android 15 QPR1 或更高版本的设备上运行的应用，系统会在状态栏中显示一个醒目的大条状标签，以提醒用户正在进行的任何屏幕投影。用户可以点按该条状标签，停止共享、投放或录制其屏幕。此外，当设备屏幕锁定时，屏幕投影会自动停止。

用于屏幕共享、投屏和录制的状态栏条状标签。

默认情况下，您的应用会包含状态栏条状标签，并会在锁定屏幕激活时自动暂停屏幕投影。

如需详细了解如何针对这些用例测试应用，请参阅

状态栏条状标签和自动停止

后台网络访问限制

在 Android 15 中，如果应用在有效的

进程生命周期

之外启动网络请求，则会收到异常。通常是

UnknownHostException

或其他与套接字相关的

IOException

。在有效生命周期之外发生的网络请求通常是因为应用在不再活跃后，不知不觉地继续发出网络请求。

为缓解此异常，请使用

生命周期感知型组件

，确保您的网络请求具有生命周期感知功能，并在离开有效的进程生命周期时取消。如果您非常重视即使用户离开应用也要发出网络请求，请考虑使用

WorkManager

调度网络请求，或使用

前台服务

继续执行对用户可见的任务。

随着每个版本的发布，特定的 Android API 可能会过时或需要进行重构，以提供更好的开发者体验或支持新的平台功能。在这些情况下，我们会正式废弃过时的 API，并引导开发者改用替代 API。

废弃意味着我们已结束对这些 API 的正式支持，但它们将继续可供开发者使用。如需详细了解此 Android 版本中值得注意的弃用，请参阅

弃用页面

本页面上的内容和代码示例受

内容许可

部分所述许可的限制。Java 和 OpenJDK 是 Oracle 和/或其关联公司的注册商标。

最后更新时间 (UTC)：2025-08-27。

[[["易于理解","easyToUnderstand","thumb-up"],["解决了我的问题","solvedMyProblem","thumb-up"],["其他","otherUp","thumb-up"]],[["没有我需要的信息","missingTheInformationINeed","thumb-down"],["太复杂/步骤太多","tooComplicatedTooManySteps","thumb-down"],["内容需要更新","outOfDate","thumb-down"],["翻译问题","translationIssue","thumb-down"],["示例/代码问题","samplesCodeIssue","thumb-down"],["其他","otherDown","thumb-down"]],["最后更新时间 (UTC)：2025-08-27。"],[],[],null,[]]

[代码块] adb install --bypass-low-target-sdk-blockFILENAME.apk

[代码] adb install --bypass-low-target-sdk-blockFILENAME.apk

[命令] adb install --bypass-low-target-sdk-blockFILENAME.apk