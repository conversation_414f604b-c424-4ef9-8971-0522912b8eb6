# 项目结构说明

## 重构后的目录结构

```
version-notice/
├── config/                     # 配置文件目录
│   ├── config_ios.yaml        # iOS 分析器配置
│   └── config_android.yaml    # Android 分析器配置
├── logs/                       # 日志文件目录
│   ├── ios_ai_analysis.log    # iOS 分析器日志
│   ├── android_ai_analysis.log # Android 分析器日志
│   ├── apple_releases.log     # Apple RSS 监控日志
│   └── android_releases.log   # Android RSS 监控日志
├── notes/                      # 原始文档目录
│   ├── ios_notes/             # iOS Release Notes
│   └── android_notes/         # Android Release Notes
├── analysis_results/           # 分析结果目录
│   ├── ios/                   # iOS 分析结果
│   └── android/               # Android 分析结果
├── universal_analyzer.py       # 通用分析器（合并后）
├── analyzer_manager.py         # 分析管理器
└── ...
```

## 主要改进

### 1. 统一分析器
- **之前**: `ios_analyzer.py` 和 `android_analyzer.py` 两个几乎相同的文件
- **现在**: `universal_analyzer.py` 一个通用分析器，通过配置区分平台

### 2. 目录结构优化
- **配置文件**: 统一放在 `config/` 目录
- **日志文件**: 统一放在 `logs/` 目录  
- **原始文档**: 统一放在 `notes/` 目录，按平台分类
- **分析结果**: 统一放在 `analysis_results/` 目录，按平台分类

### 3. 配置差异化
- **iOS 配置** (`config/config_ios.yaml`):
  - 监控目录: `notes/ios_notes`
  - 输出目录: `analysis_results/ios`
  - 日志文件: `logs/ios_ai_analysis.log`
  
- **Android 配置** (`config/config_android.yaml`):
  - 监控目录: `notes/android_notes`
  - 输出目录: `analysis_results/android`
  - 日志文件: `logs/android_ai_analysis.log`

## 使用方法

### iOS 分析
```bash
# 扫描现有文件
python analyzer_manager.py config/config_ios.yaml --scan-only

# 持续监控
python analyzer_manager.py config/config_ios.yaml
```

### Android 分析
```bash
# 扫描现有文件
python analyzer_manager.py config/config_android.yaml --scan-only

# 持续监控
python analyzer_manager.py config/config_android.yaml
```

## 代码改进

### UniversalAnalyzer 类
```python
class UniversalAnalyzer:
    def __init__(self, config, platform="universal"):
        self.platform = platform  # 平台标识
        # ... 其他初始化代码
    
    def analyze_content(self, content):
        # 根据平台设置不同的文件名标识
        platform_name = {
            "ios": "iOS Release Notes",
            "android": "Android Release Notes",
            "universal": "Release Notes"
        }.get(self.platform, "Release Notes")
        # ... 分析逻辑
```

### 向后兼容
为了保持向后兼容，仍然提供了 `IOSAnalyzer` 和 `AIAnalyzer` 类：
```python
class IOSAnalyzer(UniversalAnalyzer):
    def __init__(self, config):
        super().__init__(config, "ios")

class AIAnalyzer(UniversalAnalyzer):
    def __init__(self, config):
        super().__init__(config, "android")
```

## 优势

1. **代码复用**: 消除了重复代码，只需维护一个分析器
2. **配置驱动**: 通过配置文件控制不同平台的行为
3. **目录清晰**: 按功能和平台组织文件，便于管理
4. **扩展性强**: 添加新平台只需新增配置文件
5. **向后兼容**: 保持原有接口不变
