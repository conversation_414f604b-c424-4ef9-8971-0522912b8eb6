# AI分析配置文件
ai_analysis:
  # API配置
  api:
    provider: "openai"  # openai, anthropic, azure_openai, ollama
    model: "kimi2"  # gpt-4o, gpt-4o-mini, claude-3-5-sonnet-20241022, etc.
    api_key: "tc-lPYsGhv3Q4m1BUXt8140EdE0129f44E58704141e0c41B589"  # 环境变量或直接填写
    base_url: "http://aiinone.seasungame.com:8000/ai_in_one/v2/chat/completions"  # 可选,用于自定义API端点
    temperature: 0.3
    max_tokens: 20000
    timeout: 60

  # 提示词配置
  prompts:
    system_prompt: |
      你是一个专业的Apple开发者工具分析师。请分析Apple Developer Release Notes,重点关注会对原有App造成影响的更新,提供以下信息:
      
      1. **版本信息**:产品名称、版本号、发布日期
      2. **兼容性变化**:是否有破坏性变化或兼容性问题
      3. **开发者影响**:这些更新对开发者原有app的具体影响
      4. **推荐行动**:开发者应该采取的行动建议
      
      请用中文回答,格式清晰,重点突出,以可读性比较强的markdown结构输出。

    user_prompt_template: |
      请分析以下Apple Developer Release Notes:
      
      文件名:{filename}
      内容:
      {content}
      
      请按照系统提示的格式进行分析。

  # 输出配置
  output:
    save_analysis: true
    analysis_dir: "analysis_results"
    format: "markdown"  # markdown, json, txt
    include_timestamp: true
    include_original_filename: true

# 监控配置
monitoring:
  # 监控目录
  watch_directory: "release_notes"
  
  # 检查间隔（秒）
  check_interval: 30
  
  # 文件过滤
  file_patterns:
    - "*.txt"
    - "*.md"
  
  # 忽略的文件
  ignore_patterns:
    - ".*"  # 隐藏文件
    - "*_analyzed*"  # 已分析的文件
  
  # 处理记录
  processed_files_db: "processed_analysis.json"

# 日志配置
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/ios_ai_analysis.log"
  console: true

# 高级配置
advanced:
  # 并发处理
  max_concurrent_analysis: 3
  
  # 重试配置
  max_retries: 3
  retry_delay: 5
  
  # 文件大小限制（字节）
  max_file_size: 1048576  # 1MB
  
  # 分析缓存
  enable_cache: true
  cache_duration_hours: 24
