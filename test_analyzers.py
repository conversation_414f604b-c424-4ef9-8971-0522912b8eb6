#!/usr/bin/env python3
"""
测试分析器脚本
用于验证 iOS 和 Android 分析器是否正常工作
"""

import sys
import os
from analyzer_manager import AnalyzerManager

def test_ios_analyzer():
    """测试 iOS 分析器"""
    print("=" * 50)
    print("测试 iOS 分析器")
    print("=" * 50)
    
    try:
        config_path = "config/config_ios.yaml"
        if not os.path.exists(config_path):
            print(f"❌ 配置文件不存在: {config_path}")
            return False
        
        analyzer = AnalyzerManager(config_path, "ios")
        print(f"✅ iOS 分析器创建成功")
        print(f"   分析器类型: {type(analyzer.ai_analyzer).__name__}")
        print(f"   监控目录: {analyzer.config['monitoring']['watch_directory']}")
        print(f"   输出目录: {analyzer.config['ai_analysis']['output']['analysis_dir']}")
        print(f"   日志文件: {analyzer.config['logging']['file']}")
        
        return True
    except Exception as e:
        print(f"❌ iOS 分析器测试失败: {e}")
        return False

def test_android_analyzer():
    """测试 Android 分析器"""
    print("=" * 50)
    print("测试 Android 分析器")
    print("=" * 50)
    
    try:
        config_path = "config/config_android.yaml"
        if not os.path.exists(config_path):
            print(f"❌ 配置文件不存在: {config_path}")
            return False
        
        analyzer = AnalyzerManager(config_path, "android")
        print(f"✅ Android 分析器创建成功")
        print(f"   分析器类型: {type(analyzer.ai_analyzer).__name__}")
        print(f"   监控目录: {analyzer.config['monitoring']['watch_directory']}")
        print(f"   输出目录: {analyzer.config['ai_analysis']['output']['analysis_dir']}")
        print(f"   日志文件: {analyzer.config['logging']['file']}")
        
        return True
    except Exception as e:
        print(f"❌ Android 分析器测试失败: {e}")
        return False

def test_directory_structure():
    """测试目录结构"""
    print("=" * 50)
    print("测试目录结构")
    print("=" * 50)
    
    required_dirs = ["config", "logs", "release_notes", "android_notes", "analysis_results"]
    required_files = [
        "config/config_ios.yaml",
        "config/config_android.yaml",
        "ios_analyzer.py",
        "android_analyzer.py",
        "analyzer_manager.py"
    ]
    
    # 检查目录
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"✅ 目录存在: {dir_path}")
        else:
            print(f"❌ 目录不存在: {dir_path}")
    
    # 检查文件
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ 文件存在: {file_path}")
        else:
            print(f"❌ 文件不存在: {file_path}")

def main():
    """主函数"""
    print("开始测试分析器系统...")
    print()
    
    # 测试目录结构
    test_directory_structure()
    print()
    
    # 测试分析器
    ios_ok = test_ios_analyzer()
    print()
    android_ok = test_android_analyzer()
    print()
    
    # 总结
    print("=" * 50)
    print("测试总结")
    print("=" * 50)
    if ios_ok and android_ok:
        print("✅ 所有测试通过！系统已准备就绪。")
        print()
        print("使用方法:")
        print("  iOS 分析:     python analyzer_manager.py config/config_ios.yaml")
        print("  Android 分析: python analyzer_manager.py config/config_android.yaml")
        print("  扫描模式:     python analyzer_manager.py config/config_ios.yaml --scan-only")
    else:
        print("❌ 部分测试失败，请检查配置。")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
