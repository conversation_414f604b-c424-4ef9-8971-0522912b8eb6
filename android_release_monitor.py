#!/usr/bin/env python3
"""
Android Developer Release Monitor
监控Android版本并自动提取行为变更信息
"""

import requests
from bs4 import BeautifulSoup
import time
import json
import os
import re
from datetime import datetime
from urllib.parse import urljoin, urlparse
import logging

# 配置日志
import os
os.makedirs('logs', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/android_releases.log'),
        logging.StreamHandler()
    ]
)

class AndroidReleaseMonitor:
    def __init__(self):
        self.base_url = "https://developer.android.com"
        self.versions_url = "https://developer.android.com/about/versions?hl=zh-cn"
        self.data_file = "processed_android_releases.json"
        self.notes_dir = "android_notes"
        
        # HTTP会话配置
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        # 创建目录
        os.makedirs(self.notes_dir, exist_ok=True)
        
        # 加载已处理的版本
        self.processed_releases = self.load_processed_releases()
    
    def load_processed_releases(self):
        """加载已处理的版本记录"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return set(json.load(f))
            except Exception as e:
                logging.error(f"加载已处理版本失败: {e}")
                return set()
        return set()
    
    def save_processed_releases(self):
        """保存已处理的版本记录"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(list(self.processed_releases), f, indent=2, ensure_ascii=False)
            logging.info(f"已保存处理记录到 {self.data_file}")
        except Exception as e:
            logging.error(f"保存处理记录失败: {e}")
    
    def parse_android_versions(self):
        """解析Android版本页面，获取所有版本信息"""
        try:
            logging.info(f"正在获取Android版本页面: {self.versions_url}")
            response = self.session.get(self.versions_url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            versions = []
            
            # 查找左侧导航栏中的Android版本链接
            # 根据页面结构，查找包含版本信息的链接
            version_links = soup.find_all('a', href=re.compile(r'/about/versions/\d+'))
            
            for link in version_links:
                href = link.get('href')
                text = link.get_text(strip=True)
                
                # 提取版本号
                version_match = re.search(r'/about/versions/(\d+)', href)
                if version_match:
                    version_number = version_match.group(1)
                    full_url = urljoin(self.base_url, href)
                    
                    versions.append({
                        'version': version_number,
                        'text': text,
                        'url': full_url
                    })
            
            # 去重并排序
            unique_versions = {}
            for v in versions:
                if v['version'] not in unique_versions:
                    unique_versions[v['version']] = v
            
            sorted_versions = sorted(unique_versions.values(), 
                                   key=lambda x: int(x['version']), 
                                   reverse=True)
            
            logging.info(f"发现 {len(sorted_versions)} 个Android版本")
            return sorted_versions
            
        except Exception as e:
            logging.error(f"解析Android版本页面失败: {e}")
            return []
    
    def get_behavior_changes_url(self, version_number):
        """构建行为变更页面URL"""
        return f"https://developer.android.com/about/versions/{version_number}/behavior-changes-all?hl=zh-cn"
    
    def extract_behavior_changes(self, version_number):
        """提取指定版本的行为变更内容"""
        behavior_url = self.get_behavior_changes_url(version_number)

        try:
            logging.info(f"正在获取Android {version_number}行为变更: {behavior_url}")
            response = self.session.get(behavior_url, timeout=30)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')

            # 移除不需要的元素
            for element in soup(['script', 'style', 'nav', 'header', 'footer', 'aside']):
                element.decompose()

            # 查找主要内容区域
            content_selectors = [
                'main',
                '.devsite-main-content',
                '.devsite-article-body',
                'article',
                '.main-content',
                '.content'
            ]

            content = None
            for selector in content_selectors:
                content = soup.select_one(selector)
                if content:
                    break

            if not content:
                content = soup.find('body')

            if not content:
                logging.warning(f"未找到主要内容区域: {behavior_url}")
                return None

            # 提取所有文本内容，包括特殊的dir="ltr"元素
            all_content = []

            # 首先提取常规文本内容
            for element in content.find_all(text=True):
                if element.parent.name not in ['script', 'style']:
                    text = element.strip()
                    if text and len(text) > 2:
                        all_content.append(text)

            # 专门查找dir="ltr"的元素（通常包含代码块）
            ltr_elements = content.find_all(attrs={'dir': 'ltr'})
            for ltr_elem in ltr_elements:
                ltr_text = ltr_elem.get_text(strip=True)
                if ltr_text and ltr_text not in all_content:
                    all_content.append(f"[代码块] {ltr_text}")

            # 专门查找代码相关的元素
            code_selectors = ['pre', 'code', '.highlight', '.code-block', '.devsite-code']
            for selector in code_selectors:
                code_elements = content.select(selector)
                for code_elem in code_elements:
                    code_text = code_elem.get_text(strip=True)
                    if code_text and code_text not in all_content:
                        all_content.append(f"[代码] {code_text}")

            # 查找包含"adb"、"shell"等关键词的元素
            command_elements = content.find_all(text=re.compile(r'adb|shell|am|compat', re.I))
            for cmd_elem in command_elements:
                parent = cmd_elem.parent
                if parent:
                    parent_text = parent.get_text(strip=True)
                    if parent_text and parent_text not in all_content:
                        all_content.append(f"[命令] {parent_text}")

            # 去重并组合内容
            unique_content = []
            seen = set()
            for item in all_content:
                if item not in seen:
                    unique_content.append(item)
                    seen.add(item)

            return '\n\n'.join(unique_content)

        except Exception as e:
            logging.error(f"提取Android {version_number}行为变更失败: {e}")
            return None
    
    def save_behavior_changes(self, version_number, content):
        """保存行为变更内容到文件"""
        filename = f"android{version_number}_notes.txt"
        filepath = os.path.join(self.notes_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"Android {version_number} 行为变更\n")
                f.write(f"提取时间: {datetime.now().isoformat()}\n")
                f.write(f"来源: {self.get_behavior_changes_url(version_number)}\n")
                f.write("=" * 80 + "\n\n")
                f.write(content)
            
            logging.info(f"Android {version_number}行为变更已保存: {filepath}")
            return filepath
        except Exception as e:
            logging.error(f"保存Android {version_number}行为变更失败: {e}")
            return None
    
    def process_versions(self):
        """处理所有版本的行为变更"""
        versions = self.parse_android_versions()
        if not versions:
            logging.warning("未找到任何Android版本")
            return
        
        new_versions = []
        for version_info in versions:
            version_number = version_info['version']
            if version_number not in self.processed_releases:
                new_versions.append(version_info)
        
        if not new_versions:
            logging.info("没有新的Android版本需要处理")
            return
        
        logging.info(f"发现 {len(new_versions)} 个新版本需要处理")
        
        for version_info in new_versions:
            version_number = version_info['version']
            logging.info(f"正在处理Android {version_number}")
            
            # 提取行为变更内容
            content = self.extract_behavior_changes(version_number)
            if content:
                # 保存到文件
                filepath = self.save_behavior_changes(version_number, content)
                if filepath:
                    self.processed_releases.add(version_number)
                    logging.info(f"成功处理Android {version_number}")
            else:
                logging.warning(f"未能获取Android {version_number}的行为变更内容")
            
            # 添加延迟避免请求过快
            time.sleep(2)
        
        # 保存处理记录
        self.save_processed_releases()
    
    def run_once(self):
        """运行一次检查"""
        logging.info("开始检查Android版本行为变更...")
        self.process_versions()
        logging.info("检查完成")
    
    def run_continuous(self, interval_minutes=1):
        """持续运行监控"""
        logging.info(f"开始持续监控Android版本，检查间隔: {interval_minutes} 分钟")
        
        while True:
            try:
                self.run_once()
                time.sleep(interval_minutes * 60)
            except KeyboardInterrupt:
                logging.info("监控已停止")
                break
            except Exception as e:
                logging.error(f"监控过程中出错: {e}")
                time.sleep(60)  # 出错后等待1分钟再继续

if __name__ == "__main__":
    monitor = AndroidReleaseMonitor()
    
    # 可以选择运行一次或持续监控
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--once":
        monitor.run_once()
    else:
        monitor.run_continuous(interval_minutes=1)
