from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate

## iOS AI分析器 只做分析
class IOSAnalyzer:
    def __init__(self, config):
        self.config = config
        self.api_config = self.config['ai_analysis']['api']
        self.prompt_template = self._create_prompt_template()
        self.llm = self._create_llm()
    
    def _create_llm(self):
        # 创建API配置副本，避免修改原配置
        api_config = self.api_config.copy()
        api_config.pop('provider', None)
        return ChatOpenAI(**api_config)

    def _create_prompt_template(self):
        prompts = self.config["ai_analysis"]["prompts"]
        
        return ChatPromptTemplate.from_messages([
            ("system", prompts["system_prompt"]),
            ("human", prompts["user_prompt_template"])
        ])

    def analyze_content(self, content):
        max_size = self.config.get("advanced", {}).get("max_file_size", 1048576)
        if len(content.encode('utf-8')) > max_size:
            print(f"文件超过大小限制，跳过分析")
            return None
        
        messages = self.prompt_template.format_messages(
            filename="iOS Release Notes",
            content=content[:10000]  # 限制内容长度
        )

        return self.llm.invoke(messages).content
