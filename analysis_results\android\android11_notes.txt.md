# Android 11 行为变更分析  
> 面向所有应用（无论 `targetSdkVersion` 如何）

---

## 1. 版本信息
| 项目 | 内容 |
|---|---|
| 产品名称 | Android 11 |
| 版本号 | API 30 |
| 发布日期 | 2020-09-08（官方发布时间，文档最后更新 2025-08-27） |

---

## 2. 兼容性变化（破坏性变更速览）

| 类别 | 变更点 | 是否破坏兼容 | 影响范围 |
|---|---|---|---|
| **隐私** | 单次授权、权限“不再询问”逻辑 | ✅ | 所有应用 |
| **隐私** | `getIccId()` 返回空字符串 | ✅ | 读取 SIM ICCID 的应用 |
| **隐私** | 蓝牙扫描必须开启位置且授予位置权限（接触史通知除外） | ✅ | 使用 BLE 扫描的应用 |
| **安全** | fdsan 默认 **abort**（原为仅警告） | ✅ | 存在文件描述符错误关闭的 NDK 代码 |
| **系统库** | V1 版 Google 地图共享库 **已移除** | ✅ | 仍依赖 `com.google.android.maps` v1 的应用 |
| **NDK** | 禁止通过绝对路径 `/system/lib/libicuuc.so` 加载 ICU | ✅ | 使用 `dlopen("/system/lib/libicuuc.so")` 的 NDK 代码 |
| **非 SDK 接口** | 新增/收紧限制列表 | ✅ | 使用反射/隐藏 API 的应用 |
| **URI 共享** | 分享 URI 必须带 `FLAG_GRANT_*_URI_PERMISSION` | ✅ | 通过 `Intent` 分享 `content://` URI 的应用 |
| **应用使用统计** | `UsageStatsManager` 仅在用户解锁后可用 | ✅ | 在 `BOOT_COMPLETED` 等阶段读取使用统计的应用 |

---

## 3. 对原有 App 的具体影响

| 场景 | 可能症状 | 根因 |
|---|---|---|
| **权限弹窗** | 用户拒绝两次后，权限请求不再弹出 | 权限“不再询问”逻辑生效 |
| **SIM 卡识别** | 读取 ICCID 得到空字符串，导致无法区分 SIM | `getIccId()` 被限制 |
| **蓝牙扫描** | 扫描失败，回调无结果 | 未开启位置或未授予位置权限 |
| **Native 崩溃** | 出现 `fdsan ERROR:` 并直接崩溃 | fdsan 默认 abort |
| **地图功能** | 应用启动崩溃或地图空白 | 仍引用已移除的 V1 地图库 |
| **NDK 加载库** | `dlopen` 返回 NULL，功能缺失 | 使用绝对路径加载 ICU |
| **隐藏 API** | 反射调用抛 `NoSuchFieldError`/`Method` | 非 SDK 接口被限制 |
| **分享文件** | 接收方无法访问 URI，抛 `SecurityException` | 未设置 `FLAG_GRANT_*_URI_PERMISSION` |
| **使用统计** | `queryUsageStats()` 返回空列表 | 设备尚未解锁 |

---

## 4. 开发者推荐行动清单

### ✅ 立即检查
- **权限逻辑**：测试拒绝两次后再次请求权限的场景，确保 UI/UX 适配“不再询问”。
- **SIM 识别**：将 `getIccId()` 替换为 `SubscriptionManager.getSubscriptionId()`。
- **蓝牙功能**：在 Android 11 真机上验证 BLE 扫描流程，确保已申请 `ACCESS_FINE_LOCATION` 并引导用户开启位置。
- **Native 崩溃**：在 `debug` 版本打开 ASan/fdsan 诊断，修复 `close(fd)` 后再使用或重复关闭的问题。
- **地图依赖**：移除 `com.google.android.maps` v1 相关代码，迁移到 [Maps SDK v3](https://developers.google.com/maps/documentation/android-sdk/v3-client-migration)。
- **NDK 加载**：将 `dlopen("/system/lib/libicuuc.so", ...)` 改为 `dlopen("libicuuc.so", ...)`。
- **非 SDK 接口**：运行 [veridex](https://developer.android.com/about/versions/11/non-sdk-11) 工具扫描，替换为官方 SDK 或申请新 API。
- **URI 共享**：所有 `Intent` 分享 `content://` 时，务必添加  
  ```java
  intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
  ```
- **UsageStats**：在 `isUserUnlocked()` 为 `true` 后再调用 `UsageStatsManager`，或监听 `ACTION_USER_UNLOCKED` 广播。

### ✅ 测试建议
- 使用 Android 11 真机 + 模拟器（API 30）全面回归。
- 打开 `strictMode` + `veridex` 做静态扫描。
- 对 NDK 代码打开 `fdsan` 诊断模式：  
  ```
  adb shell setenforce 0
  adb shell setprop debug.fdsan.enabled 1
  ```

---

> 完成以上检查后，再查看“仅影响 targetSdkVersion=30”的变更列表，确保升级路径平滑。