import os
import time
import logging
import json

from config_reader import config_reader
from file_monitor import FileMonitor
from android_analyzer import AIAnalyzer
from ios_analyzer import IOSAnalyzer

from watchdog.observers import Observer

## 分析管理器 log 调度 文件夹控制
class AnalyzerManager:
    def __init__(self, config, analyzer_type="android"):
        self.config_manager = config_reader(config)
        self.config = self.config_manager.config

        # 根据类型选择分析器
        if analyzer_type.lower() == "ios":
            self.ai_analyzer = IOSAnalyzer(self.config)
        else:
            self.ai_analyzer = AIAnalyzer(self.config)

        self._setup_logging()
        
        self.output_config = self.config['ai_analysis']['output']
        self.output_dir = self.output_config.get("analysis_dir")
        self._create_directories()

        self.processed_files = self._load_processed_files()

    # 创建必要目录
    def _create_directories(self):
        analysis_dir = self.output_dir
        os.makedirs(analysis_dir, exist_ok=True)

    ## 分析并保存
    def analyze(self,filepath):
        name = os.path.basename(filepath)
       

        if(name in self.processed_files):
            logging.info(f"文件已处理: {name}")
            return
        logging.info(f"开始分析文件: {name}")
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
            analysis = self.ai_analyzer.analyze_content(content)
        if analysis:
            open(os.path.join(self.output_dir, name + '.md'), 'w', encoding='utf-8').write(analysis)
            self.processed_files.add(name)
            self._save_processed_files()
        else:
            print(f"分析失败: {filepath}")

    ## 启动监控
    def start_monitoring(self):
        """开始监控"""
        watch_dir = self.config["monitoring"]["watch_directory"]
        
        # 先扫描现有文件
        self.scan_existing_files()
        
        # 设置文件监控
        event_handler = FileMonitor(self)
        observer = Observer()
        observer.schedule(event_handler, watch_dir, recursive=True)
        
        logging.info(f"开始监控目录: {watch_dir}")
        observer.start()
        
        try:
            while True:
                time.sleep(self.config["monitoring"].get("check_interval", 30))
        except KeyboardInterrupt:
            logging.info("停止监控")
            observer.stop()
        
        observer.join()

    ## log记录
    def _setup_logging(self):
        log_config = self.config.get("logging")

        handlers = []
        if log_config.get("console", True):
            handlers.append(logging.StreamHandler())
        
        if log_config.get("file"):
            handlers.append(logging.FileHandler(log_config["file"], encoding='utf-8'))
        
        logging.basicConfig(
            level=getattr(logging, log_config.get("level", "INFO")),
            format=log_config.get("format", "%(asctime)s - %(levelname)s - %(message)s"),
            handlers=handlers,
            force=True
        )
        
    ## 保存已经处理的文件
    def _save_processed_files(self):
        with open("processed_analysis.json", 'a') as f:
            json.dump(list(self.processed_files), f, indent=2, ensure_ascii=False)

    ## 加载已经处理的文件
    def _load_processed_files(self):
        if os.path.exists("processed_analysis.json"):
            with open("processed_analysis.json", 'r') as f:
                return set(json.load(f))
        return set()
    
    def scan_existing_files(self):
        """扫描现有文件"""
        watch_dir = self.config["monitoring"]["watch_directory"]
        if not os.path.exists(watch_dir):
            logging.warning(f"监控目录不存在: {watch_dir}")
            return
        
        logging.info(f"扫描现有文件: {watch_dir}")
        
        for root, _, files in os.walk(watch_dir):
            for file in files:
                filepath = os.path.join(root, file)
                self.analyze(filepath)
    
if __name__ == "__main__":
    import sys

    # 默认配置和分析器类型
    config_path = "config/config_android.yaml"
    analyzer_type = "android"

    # 解析命令行参数
    if len(sys.argv) > 1:
        config_path = sys.argv[1]

    # 根据配置文件名自动判断分析器类型
    if "ios" in config_path.lower():
        analyzer_type = "ios"
    elif "android" in config_path.lower():
        analyzer_type = "android"

    # 也可以通过命令行参数指定类型
    if len(sys.argv) > 3 and sys.argv[3] in ["ios", "android"]:
        analyzer_type = sys.argv[3]

    print(f"使用 {analyzer_type.upper()} 分析器，配置文件: {config_path}")
    analyzer = AnalyzerManager(config_path, analyzer_type)

    if len(sys.argv) > 2 and sys.argv[2] == "--scan-only":
        analyzer.scan_existing_files()
    else:
        analyzer.start_monitoring()