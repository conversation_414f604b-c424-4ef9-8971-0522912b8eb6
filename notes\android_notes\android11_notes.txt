Android 11 行为变更
提取时间: 2025-09-02T11:09:39.673048
来源: https://developer.android.com/about/versions/11/behavior-changes-all?hl=zh-cn
================================================================================

此页面由

Cloud Translation API

翻译。

Android Developers

基本知识

行为变更：所有应用

使用集合让一切井井有条

根据您的偏好保存内容并对其进行分类。

Android 11 平台包含一些行为变更，这些变更可能会影响您的应用。以下行为变更将影响在 Android 11 上运行的所有应用，无论其采用哪种

targetSdkVersion

都不例外。

您应该测试您的应用，然后根据需要进行修改，以适当地支持这些变更。

此外，请务必查看

仅影响以 Android 11 为目标平台的应用的行为变更

列表。

隐私设置

Android 11 引入了一些变更和限制来加强用户隐私保护，其中包括：

单次授权

：让用户可以选择授予更多对位置信息、麦克风和摄像头的临时访问权限。

权限对话框的可见性

：一再拒绝某项权限表示用户希望“不再询问”。

数据访问审核

：深入了解您的应用在何处访问私密数据，无论是在您的应用自己的代码中，还是在依赖库的代码中。

系统提醒窗口权限

：根据请求自动向某些类型的应用授予

SYSTEM_ALERT_WINDOW

权限。此外，包含

ACTION_MANAGE_OVERLAY_PERMISSION

intent 操作的 intent 始终会将用户转至系统设置中的屏幕。

永久 SIM 卡标识符

：在 Android 11 及更高版本中，使用

getIccId()

方法访问不可重置的 ICCID 受到限制。该方法会返回一个非 null 的空字符串。如需唯一标识设备上安装的 SIM 卡，请改用

getSubscriptionId()

方法。订阅 ID 会提供一个索引值（从 1 开始），用于唯一识别已安装的 SIM 卡（包括实体 SIM 卡和电子 SIM 卡）。除非设备恢复出厂设置，否则此标识符的值对于给定 SIM 卡是保持不变的。

如需了解详情，请参阅

页面。

接触史通知

Android 11 在更新平台时考虑了

接触史通知系统

。用户现已可在 Android 11 上运行接触史通知应用，且无需开启设备位置信息设置。接触史通知系统的设计使得使用该系统的应用无法通过蓝牙扫描推断设备所处的位置，因此，此例外情况仅适用于接触史通知系统。

为保护用户的隐私，所有其他应用仍无法执行蓝牙扫描，除非用户已开启设备位置信息设置且已授予相应应用位置权限。如需了解详情，请阅读我们的

接触史通知最新动态

博文。

安全性

SSL 套接字默认情况下使用 Conscrypt SSL 引擎

Android 的默认

SSLSocket

实现基于

Conscrypt

。从 Android 11 开始，该实现是在 Conscrypt 的

SSLEngine

之上内部构建的。

Scudo Hardened Allocator

Android 11 在内部使用

为堆分配提供服务。Scudo 能够检测并减轻某些类型的内存安全违规行为。如果您在原生代码崩溃报告中发现与 Scudo 相关的崩溃（例如

Scudo ERROR:

），请参阅

Scudo 问题排查

文档。

应用使用情况统计信息

为了更好地保护用户，Android 11 将每个用户的应用使用情况统计信息存储在

凭据加密存储空间

中。因此，系统和任何应用都无法访问该数据，除非

isUserUnlocked()

true

，这发生在出现以下某种情况之后：

用户在系统启动后首次解锁其设备。

用户在设备上切换到自己的账号。

如果您的应用已绑定到

UsageStatsManager

的实例，请检查您是否是在用户解锁其设备后在此对象上调用方法。如果并非如此，该 API 现在会返回 null 或空值。

针对 5G 的模拟器支持

Android 11 添加了

5G API

，使您的应用能够添加各种先进的功能。如需在添加这些功能时对其进行测试，您可以使用

Android SDK 模拟器

的新功能。这项新功能是在模拟器版本 30.0.22 中添加的。选择 5G 网络设置可将

TelephonyDisplayInfo

OVERRIDE_NETWORK_TYPE_NR_NSA

，修改带宽估算值，还允许您设置按流量计费性，以验证您的应用是否会对

NET_CAPABILITY_TEMPORARILY_NOT_METERED

状态的变化做出适当的响应。

性能和调试

JobScheduler API 调用限制调试

Android 11 为应用提供调试支持，以便确定有可能超过特定速率限制的

JobScheduler

API 调用。开发者可以利用此服务发现潜在的性能问题。对于

debuggable

清单属性设置为 true 的应用，超出速率限制的

API 调用将返回

RESULT_FAILURE

。如此设置限制，正当合理的用例应该就不会受到影响。

文件描述符排错程序 (fdsan)

Android 10 引入了

fdsan

（文件描述符排错程序）。

检测错误处理文件描述符所有权的错误，例如 use-after-close 和 double-close。在 Android 11 中，

的默认模式发生了变化。现在，

会在检测到错误时中止，而以前的行为则是记录警告并继续。如果您在应用中发现由于

而导致的崩溃，请参阅

fdsan documentation

非 SDK 接口限制

Android 11 包含更新后的受限制非 SDK 接口列表（基于与 Android 开发者之间的协作以及最新的内部测试）。在限制使用非 SDK 接口之前，我们会尽可能确保有可用的公开替代方案。

如果您的应用并非以 Android 11 为目标平台，那么其中一些变更可能不会立即对您产生影响。然而，虽然您目前仍可以使用一些非 SDK 接口（

具体取决于应用的目标 API 级别

），但只要您使用任何非 SDK 方法或字段，终归存在导致应用出问题的显著风险。

如果您不确定自己的应用是否使用了非 SDK 接口，则可以

测试您的应用

来进行确认。如果您的应用依赖于非 SDK 接口，您应该开始计划迁移到 SDK 替代方案。然而，我们知道某些应用具有使用非 SDK 接口的有效用例。如果您无法为应用中的某项功能找到使用非 SDK 接口的替代方案，应

请求新的公共 API

如需详细了解此 Android 版本中的变更，请参阅

Android 11 中有关限制非 SDK 接口的更新

。如需全面了解有关非 SDK 接口的详细信息，请参阅

对非 SDK 接口的限制

V1 版 Google 地图共享库已移除

Android 11 中已完全移除 V1 版 Google 地图共享库。此库之前已被弃用，并已停止在 Android 10 中的应用中运行。对于搭载 Android 9（API 级别 28）或更低版本的设备，之前依赖于此共享库的应用应改用

适用于 Android 的 Google 地图 SDK

与其他应用交互

分享内容 URI

如果您的应用与其他应用分享内容 URI，相应 intent 必须至少设置以下 intent 标记中的一个，以便

授予对 URI 的访问权限

FLAG_GRANT_READ_URI_PERMISSION

FLAG_GRANT_WRITE_URI_PERMISSION

。这样一来，如果其他应用以 Android 11 为目标平台，相应应用仍可访问内容 URI。即使内容 URI 与不属于您的应用的内容提供程序相关联，您的应用也必须包含 intent 标记。

如果您的应用拥有已与内容 URI 相关联的内容提供程序，请确认

该内容提供程序未被导出

。我们已建议采用这项安全最佳做法。

正在加载库

使用绝对路径加载 ICU 通用库

以 API 28 及更低版本为目标平台的应用无法使用

dlopen(3)

加载绝对路径为“/system/lib/libicuuc.so”的

libicuuc

。对于这些应用，

dlopen("/system/lib/libicuuc.so", ...)

将返回 null 句柄。

请改为使用库名称作为文件名来加载库，例如

dlopen("libicuuc.so", ...)

本页面上的内容和代码示例受

内容许可

部分所述许可的限制。Java 和 OpenJDK 是 Oracle 和/或其关联公司的注册商标。

最后更新时间 (UTC)：2025-08-27。

[[["易于理解","easyToUnderstand","thumb-up"],["解决了我的问题","solvedMyProblem","thumb-up"],["其他","otherUp","thumb-up"]],[["没有我需要的信息","missingTheInformationINeed","thumb-down"],["太复杂/步骤太多","tooComplicatedTooManySteps","thumb-down"],["内容需要更新","outOfDate","thumb-down"],["翻译问题","translationIssue","thumb-down"],["示例/代码问题","samplesCodeIssue","thumb-down"],["其他","otherDown","thumb-down"]],["最后更新时间 (UTC)：2025-08-27。"],[],[],null,[]]